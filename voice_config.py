"""
Voice configuration settings for the YouTube Shorts generator.
Customize these settings to control voice generation behavior.
"""

# Voice Generation Settings
VOICE_SETTINGS = {
    # Primary TTS engine: 'edge-tts' (recommended) or 'gtts' (fallback)
    'primary_engine': 'edge-tts',
    
    # Whether to use random voices for variety (True) or stick to one voice (False)
    'use_random_voices': True,
    
    # Default voice if not using random voices
    'default_voice': 'en-US-AriaNeural',
    
    # Fallback to gTTS if Edge-TTS fails
    'fallback_to_gtts': True,
}

# Available high-quality Edge-TTS voices
EDGE_TTS_VOICES = {
    # Female voices
    'en-US-AriaNeural': {
        'gender': 'female',
        'style': 'conversational',
        'description': 'Natural and conversational, great for facts'
    },
    'en-US-JennyNeural': {
        'gender': 'female', 
        'style': 'friendly',
        'description': 'Friendly and warm, perfect for engaging content'
    },
    'en-US-AmberNeural': {
        'gender': 'female',
        'style': 'professional',
        'description': 'Warm and professional, authoritative tone'
    },
    'en-US-AnaNeural': {
        'gender': 'female',
        'style': 'energetic',
        'description': 'Young and energetic, great for exciting facts'
    },
    'en-US-ElizabethNeural': {
        'gender': 'female',
        'style': 'sophisticated',
        'description': 'Mature and sophisticated, perfect for educational content'
    },
    'en-US-MichelleNeural': {
        'gender': 'female',
        'style': 'clear',
        'description': 'Professional and clear, excellent articulation'
    },
    
    # Male voices
    'en-US-GuyNeural': {
        'gender': 'male',
        'style': 'conversational', 
        'description': 'Conversational and clear, natural delivery'
    },
    'en-US-DavisNeural': {
        'gender': 'male',
        'style': 'expressive',
        'description': 'Expressive and engaging, great for storytelling'
    },
    'en-US-ChristopherNeural': {
        'gender': 'male',
        'style': 'authoritative',
        'description': 'Mature and authoritative, perfect for serious topics'
    },
    'en-US-EricNeural': {
        'gender': 'male',
        'style': 'enthusiastic',
        'description': 'Young and enthusiastic, energetic delivery'
    },
}

# Voice selection preferences by topic
TOPIC_VOICE_PREFERENCES = {
    'science': ['en-US-ElizabethNeural', 'en-US-ChristopherNeural', 'en-US-AmberNeural'],
    'technology': ['en-US-DavisNeural', 'en-US-AnaNeural', 'en-US-EricNeural'],
    'animals': ['en-US-JennyNeural', 'en-US-AriaNeural', 'en-US-GuyNeural'],
    'space': ['en-US-ChristopherNeural', 'en-US-ElizabethNeural', 'en-US-DavisNeural'],
    'history': ['en-US-AmberNeural', 'en-US-ChristopherNeural', 'en-US-ElizabethNeural'],
    'cars': ['en-US-EricNeural', 'en-US-DavisNeural', 'en-US-GuyNeural'],
    'food and drink': ['en-US-JennyNeural', 'en-US-AriaNeural', 'en-US-MichelleNeural'],
    'geography': ['en-US-AmberNeural', 'en-US-GuyNeural', 'en-US-ElizabethNeural'],
}

def get_voice_for_topic(topic):
    """Get a suitable voice for a specific topic."""
    import random
    
    if not VOICE_SETTINGS['use_random_voices']:
        return VOICE_SETTINGS['default_voice']
    
    # Get topic-specific voices or fall back to all voices
    preferred_voices = TOPIC_VOICE_PREFERENCES.get(topic.lower(), list(EDGE_TTS_VOICES.keys()))
    
    return random.choice(preferred_voices)

def get_voice_description(voice_name):
    """Get description for a voice."""
    voice_info = EDGE_TTS_VOICES.get(voice_name, {})
    return voice_info.get('description', 'High-quality neural voice')
