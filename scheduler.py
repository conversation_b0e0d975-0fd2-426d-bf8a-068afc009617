import schedule
import time
import logging
import subprocess

# Optional: if you have a function in your code to call directly, you can import it:
# from main_code import main   # if your existing script had a "main()" function

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def run_script():
    """
    Call your existing script.
    Option A: Direct function call (if you have one).
    Option B: Subprocess call (works for any script).
    """
    logger.info("Starting the generation & upload script now...")
    # --- Option A: If your script has a main function you can import ---
    # main()

    # --- Option B: If your script is a standalone file, just call via subprocess ---
    subprocess.run(["python", "youtubeshorts.py"], check=True)

# Schedule jobs:
schedule.every().day.at("09:00").do(run_script)  # 9 AM
schedule.every().day.at("17:00").do(run_script)  # 5 PM

logger.info("Scheduler started. Will run at 9:00 AM and 5:00 PM daily...")

# Keep the script running forever:
if __name__ == "__main__":
    while True:
        schedule.run_pending()
        time.sleep(60)
