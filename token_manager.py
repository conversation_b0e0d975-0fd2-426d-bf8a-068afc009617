#!/usr/bin/env python3
"""
Token Manager - Utility to backup and restore YouTube API tokens
This helps prevent losing authentication when tokens get corrupted.
"""

import os
import json
import shutil
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

TOKEN_FILE = "token.json"
BACKUP_DIR = "token_backups"

def create_backup_dir():
    """Create backup directory if it doesn't exist"""
    if not os.path.exists(BACKUP_DIR):
        os.makedirs(BACKUP_DIR)
        logger.info(f"Created backup directory: {BACKUP_DIR}")

def backup_token():
    """Backup the current token.json file"""
    if not os.path.exists(TOKEN_FILE):
        logger.warning(f"No {TOKEN_FILE} file found to backup")
        return False
    
    create_backup_dir()
    
    # Create timestamped backup filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"token_backup_{timestamp}.json"
    backup_path = os.path.join(BACKUP_DIR, backup_filename)
    
    try:
        # Verify token is valid JSON before backing up
        with open(TOKEN_FILE, 'r') as f:
            token_data = json.load(f)
        
        # Copy to backup location
        shutil.copy2(TOKEN_FILE, backup_path)
        logger.info(f"Token backed up successfully to: {backup_path}")
        
        # Also create a "latest" backup
        latest_backup = os.path.join(BACKUP_DIR, "token_latest.json")
        shutil.copy2(TOKEN_FILE, latest_backup)
        logger.info(f"Latest backup updated: {latest_backup}")
        
        return True
        
    except json.JSONDecodeError:
        logger.error(f"Current {TOKEN_FILE} is not valid JSON - cannot backup")
        return False
    except Exception as e:
        logger.error(f"Failed to backup token: {e}")
        return False

def restore_token(backup_filename=None):
    """Restore token from backup"""
    create_backup_dir()
    
    if backup_filename is None:
        # Use latest backup
        backup_path = os.path.join(BACKUP_DIR, "token_latest.json")
    else:
        backup_path = os.path.join(BACKUP_DIR, backup_filename)
    
    if not os.path.exists(backup_path):
        logger.error(f"Backup file not found: {backup_path}")
        return False
    
    try:
        # Verify backup is valid JSON
        with open(backup_path, 'r') as f:
            token_data = json.load(f)
        
        # Backup current token if it exists
        if os.path.exists(TOKEN_FILE):
            backup_current = f"{TOKEN_FILE}.backup_before_restore"
            shutil.copy2(TOKEN_FILE, backup_current)
            logger.info(f"Current token backed up to: {backup_current}")
        
        # Restore from backup
        shutil.copy2(backup_path, TOKEN_FILE)
        logger.info(f"Token restored successfully from: {backup_path}")
        return True
        
    except json.JSONDecodeError:
        logger.error(f"Backup file is not valid JSON: {backup_path}")
        return False
    except Exception as e:
        logger.error(f"Failed to restore token: {e}")
        return False

def list_backups():
    """List all available token backups"""
    create_backup_dir()
    
    backups = []
    for filename in os.listdir(BACKUP_DIR):
        if filename.startswith("token_backup_") and filename.endswith(".json"):
            filepath = os.path.join(BACKUP_DIR, filename)
            stat = os.stat(filepath)
            backups.append({
                'filename': filename,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime)
            })
    
    if not backups:
        logger.info("No token backups found")
        return
    
    logger.info("Available token backups:")
    for backup in sorted(backups, key=lambda x: x['modified'], reverse=True):
        logger.info(f"  {backup['filename']} - {backup['modified']} ({backup['size']} bytes)")

def check_token_health():
    """Check if current token is healthy"""
    if not os.path.exists(TOKEN_FILE):
        logger.warning(f"No {TOKEN_FILE} file found")
        return False
    
    try:
        with open(TOKEN_FILE, 'r') as f:
            token_data = json.load(f)
        
        required_fields = ['token', 'refresh_token', 'client_id', 'client_secret']
        missing_fields = [field for field in required_fields if field not in token_data]
        
        if missing_fields:
            logger.warning(f"Token missing required fields: {missing_fields}")
            return False
        
        # Check if token has expired
        if 'expiry' in token_data:
            from datetime import datetime
            expiry = datetime.fromisoformat(token_data['expiry'].replace('Z', '+00:00'))
            if expiry < datetime.now(expiry.tzinfo):
                logger.info("Token has expired but has refresh_token - should auto-refresh")
            else:
                logger.info(f"Token is valid until: {expiry}")
        
        logger.info("Token appears healthy")
        return True
        
    except json.JSONDecodeError:
        logger.error(f"Current {TOKEN_FILE} is not valid JSON")
        return False
    except Exception as e:
        logger.error(f"Error checking token health: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python token_manager.py backup     - Backup current token")
        print("  python token_manager.py restore    - Restore from latest backup")
        print("  python token_manager.py restore <filename> - Restore from specific backup")
        print("  python token_manager.py list       - List all backups")
        print("  python token_manager.py check      - Check token health")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "backup":
        backup_token()
    elif command == "restore":
        if len(sys.argv) > 2:
            restore_token(sys.argv[2])
        else:
            restore_token()
    elif command == "list":
        list_backups()
    elif command == "check":
        check_token_health()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
