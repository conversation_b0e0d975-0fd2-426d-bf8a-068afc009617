import requests
import openai
from gtts import gTTS
from PIL import Image
from moviepy.editor import ImageSequence<PERSON><PERSON>, AudioFileClip, CompositeVideoClip, CompositeAudioClip
import numpy as np
import os

# Step 1: Generate 4 Inspirational Images (Using Aitubo API)
def generate_images(prompt, output_folder="images"):
    api_key = "api-4e3f2bdcc87311ef987ee2e18d395e7f"
    model_id = "66b1cd96bfac25af6f5e0ad9"
    response = requests.post(
        "https://creator.aitubo.ai/api/job/create",
        headers={"Authorization": f"Bearer {api_key}"},
        json={"prompt": prompt, "modelId": model_id}
    )
    if response.status_code == 200:
        job_id = response.json()["data"]["id"]
        print(f"Image generation job started. Job ID: {job_id}")
        # Wait for job to complete and download the images (implementation depends on Aitubo API)
        # For now, assume the images are saved as image_0.jpg, image_1.jpg, image_2.jpg, image_3.jpg
        os.makedirs(output_folder, exist_ok=True)
        for i in range(4):
            img_url = f"https://example.com/image_{i}.jpg"  # Replace with actual image URL
            img_data = requests.get(img_url).content
            with open(f"{output_folder}/image_{i}.jpg", "wb") as f:
                f.write(img_data)
        print(f"Images saved to {output_folder}")
    else:
        print(f"Error: {response.status_code}, {response.text}")

# Step 2: Create a Slideshow GIF or Video
def create_slideshow(image_folder, output_path="slideshow.mp4", duration_per_image=3):
    # Load all images from the folder
    images = sorted([os.path.join(image_folder, f) for f in os.listdir(image_folder) if f.endswith(".jpg")])
    if not images:
        raise ValueError("No images found in the folder.")

    # Create a slideshow video
    clip = ImageSequenceClip(images, durations=[duration_per_image] * len(images))
    clip.write_videofile(output_path, fps=24)
    print(f"Slideshow saved to {output_path}")

# Step 3: Generate Motivational Narration
def generate_narration(prompt, output_path="motivational_narration.mp3"):
    openai.api_key = "********************************************************************************************************************************************************************"

    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}]
    )

    script = response.choices[0].message.content
    print(f"Generated Script:\n{script}")

    # Convert script to audio
    tts = gTTS(script, lang="en", slow=False)
    tts.save(output_path)
    print(f"Narration saved to {output_path}")

# Step 4: Combine Slideshow and Narration into Video
def create_video(slideshow_path, narration_path, output_path="final_video.mp4"):
    # Load the slideshow and audio
    slideshow_clip = VideoFileClip(slideshow_path)
    audio_clip = AudioFileClip(narration_path)

    # Set the duration of the slideshow to match the audio
    slideshow_clip = slideshow_clip.set_duration(audio_clip.duration)

    # Combine the slideshow and audio
    final_clip = CompositeVideoClip([slideshow_clip.set_audio(audio_clip)])

    # Save the final video
    final_clip.write_videofile(output_path, fps=24)
    print(f"Video saved to {output_path}")

# Step 5: Add Background Music
def add_background_music(video_path, music_path, output_path="final_video_with_music.mp4"):
    # Load the video and background music
    video_clip = VideoFileClip(video_path)
    music_clip = AudioFileClip(music_path).volumex(0.3)  # Lower the volume

    # Combine the video audio and music
    final_audio = CompositeAudioClip([video_clip.audio, music_clip])

    # Add the combined audio to the video
    final_clip = video_clip.set_audio(final_audio)

    # Save the final video
    final_clip.write_videofile(output_path, fps=24)
    print(f"Video with background music saved to {output_path}")

# Main Workflow
def main():
    # Step 1: Generate 4 Images
    image_prompt = "A majestic mountain at sunrise, epic and inspiring"
    generate_images(image_prompt, "images")

    # Step 2: Create Slideshow
    create_slideshow("images", "slideshow.mp4", duration_per_image=3)

    # Step 3: Generate Narration
    narration_prompt = "Write a short motivational story about overcoming challenges."
    generate_narration(narration_prompt, "motivational_narration.mp3")

    # Step 4: Create Video
    create_video("slideshow.mp4", "motivational_narration.mp3", "final_video.mp4")

    # Step 5: Add Background Music
    add_background_music("final_video.mp4", "background_music.mp3", "final_video_with_music.mp4")

if __name__ == "__main__":
    main()