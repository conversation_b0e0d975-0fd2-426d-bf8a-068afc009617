from PIL import Image
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Image Dimensions
PROFILE_PIC_SIZE = (800, 800)  # YouTube requires a square image (800x800 recommended)
BANNER_SIZE = (2048, 1152)  # Minimum banner size (2048x1152)

# Paths
PROFILE_PIC_PATH = "profile_picture.jpg"
BANNER_PATH = "banner.jpg"


def resize_image(image_path, size, output_path=None):
    """
    Resize an image to the specified dimensions while maintaining aspect ratio.
    """
    try:
        with Image.open(image_path) as img:
            # Calculate the target aspect ratio
            target_width, target_height = size
            target_aspect_ratio = target_width / target_height

            # Calculate the current aspect ratio
            width, height = img.size
            aspect_ratio = width / height

            # Crop the image to match the target aspect ratio
            if aspect_ratio > target_aspect_ratio:
                # Image is wider than the target aspect ratio
                new_width = int(height * target_aspect_ratio)
                left = (width - new_width) // 2
                img = img.crop((left, 0, left + new_width, height))
            elif aspect_ratio < target_aspect_ratio:
                # Image is taller than the target aspect ratio
                new_height = int(width / target_aspect_ratio)
                top = (height - new_height) // 2
                img = img.crop((0, top, width, top + new_height))

            # Resize the image to the target dimensions
            img = img.resize(size, Image.Resampling.LANCZOS)
            if output_path:
                img.save(output_path)
            else:
                img.save(image_path)  # Overwrite the original image
            logger.info(f"Image resized to {size}: {image_path}")
    except Exception as e:
        logger.error(f"Error resizing image: {e}")


def validate_banner_image(image_path):
    """
    Validate the banner image to ensure it meets YouTube's requirements.
    """
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            file_size = os.path.getsize(image_path)

            # Check dimensions
            if width < 2048 or height < 1152:
                logger.error(f"Banner dimensions are too small: {width}x{height}. Minimum required: 2048x1152.")
                return False

            # Check file size
            if file_size > 6 * 1024 * 1024:  # 6 MB
                logger.error(f"Banner file size is too large: {file_size / 1024 / 1024:.2f} MB. Maximum allowed: 6 MB.")
                return False

            # Check aspect ratio
            aspect_ratio = width / height
            if abs(aspect_ratio - 16 / 9) > 0.1:  # Allow slight deviation
                logger.error(f"Banner aspect ratio is not 16:9: {aspect_ratio:.2f}.")
                return False

            logger.info("Banner image meets YouTube's requirements.")
            return True
    except Exception as e:
        logger.error(f"Error validating banner image: {e}")
        return False


def main():
    # Step 1: Resize the profile picture
    if os.path.exists(PROFILE_PIC_PATH):
        resize_image(PROFILE_PIC_PATH, PROFILE_PIC_SIZE)
    else:
        logger.error(f"Profile picture not found: {PROFILE_PIC_PATH}")

    # Step 2: Resize and validate the banner
    if os.path.exists(BANNER_PATH):
        try:
            # Attempt to open the image to check if it's valid
            with Image.open(BANNER_PATH) as img:
                img.verify()  # Verify that the file is a valid image
            resize_image(BANNER_PATH, BANNER_SIZE)
            if not validate_banner_image(BANNER_PATH):
                logger.error("Banner image does not meet YouTube's requirements.")
        except Exception as e:
            logger.error(f"Invalid banner image: {e}")
    else:
        logger.error(f"Banner not found: {BANNER_PATH}")


if __name__ == "__main__":
    main()



"All the facts, images, and content in this video are generated using artificial intelligence (AI). While we strive for accuracy, AI-generated content may sometimes contain errors or inconsistencies. We encourage you to do your own research and verify any facts or information presented in this video. Our goal is to inspire curiosity and learning, but it’s always a good idea to cross-check information from reliable sources. Thank you for watching, and remember: Stay curious, stay informed!"