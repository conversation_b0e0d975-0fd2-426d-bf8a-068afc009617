# youtube_uploader.py
import os
import google.auth.exceptions
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload

# Scopes let you access specific parts of a user's account.
# For uploads + setting thumbnails, we need 'youtube.upload'
SCOPES = ["https://www.googleapis.com/auth/youtube.upload"]


def get_authenticated_service(client_secrets_file="client_secret.json"):
    """
    1) Checks if we have a stored token.
    2) If not, runs an OAuth flow in the browser.
    3) Returns an authenticated YouTube API resource.
    """
    creds = None
    token_file = "token.json"

    # If we already have a valid token, load it.
    if os.path.exists(token_file):
        creds = Credentials.from_authorized_user_file(token_file, SCOPES)

    # If no valid creds, do the OAuth flow
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
            except google.auth.exceptions.RefreshError:
                creds = None
        if not creds or not creds.valid:
            flow = InstalledAppFlow.from_client_secrets_file(client_secrets_file, SCOPES)
            creds = flow.run_local_server(port=8080, prompt="consent")
        # Save the credentials for future runs
        with open(token_file, 'w') as token:
            token.write(creds.to_json())

    return build('youtube', 'v3', credentials=creds)


def upload_video(video_file,
                 title="My Great Video",
                 description="This is an auto-uploaded video!",
                 tags=None,
                 category_id="22"):  # 22 = People & Blogs by default
    """
    Uploads a video to YouTube with the given metadata.
    Returns the uploaded video's ID.
    """
    if tags is None:
        tags = ["awesome", "facts", "trending"]

    youtube = get_authenticated_service()

    request_body = {
        "snippet": {
            "title": title,
            "description": description,
            "tags": tags,
            "categoryId": category_id
        },
        "status": {
            "privacyStatus": "public"  # or "unlisted", "private"
        }
    }

    media = MediaFileUpload(video_file, chunksize=-1, resumable=True)

    request = youtube.videos().insert(
        part="snippet,status",
        body=request_body,
        media_body=media
    )
    response = None
    while response is None:
        status, response = request.next_chunk()
        if status:
            print(f"Uploaded {int(status.progress() * 100)}%...")
    print("Video upload complete!")

    # Extract the video ID from response
    video_id = response.get("id")
    print(f"Video ID: {video_id}")
    return video_id


def set_thumbnail(video_id, thumbnail_path):
    """
    Sets the custom thumbnail for a given uploaded video.
    """
    youtube = get_authenticated_service()
    request = youtube.thumbnails().set(
        videoId=video_id,
        media_body=MediaFileUpload(thumbnail_path)
    )
    response = request.execute()
    print("Thumbnail set!")
    return response


if __name__ == "__main__":
    # Example usage:

    # 1) Provide your client_secrets file path if it's not named "client_secret.json".
    # 2) Make sure "output_video.mp4" is the file you want to upload.
    # 3) Provide a thumbnail path if you have one; otherwise, skip or handle an exception.

    VIDEO_FILE = "output_video.mp4"
    THUMBNAIL_FILE = "image_0.jpg"  # Maybe use the first Aitubo image as a thumbnail?

    video_id = upload_video(
        video_file=VIDEO_FILE,
        title="Did you know? (Auto-upload)",
        description="Automated upload using Python!",
        tags=["did you know", "fun facts", "ai video"]
    )
    # Optionally set thumbnail
    set_thumbnail(video_id, THUMBNAIL_FILE)
