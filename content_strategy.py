"""
Advanced content strategy engine for creating viral, engaging YouTube Shorts.
Includes trending topics, viral formats, hooks, and retention strategies.
"""

import random
import requests
import json
import logging
from datetime import datetime, timedelta
import calendar

logger = logging.getLogger(__name__)

class ContentStrategy:
    """Advanced content strategy for viral YouTube Shorts."""
    
    def __init__(self):
        self.viral_hooks = {
            'curiosity': [
                "You won't believe what happens when...",
                "Scientists just discovered something shocking about...",
                "This will change everything you know about...",
                "The secret behind {} that nobody talks about",
                "What they don't want you to know about...",
            ],
            'countdown': [
                "3 mind-blowing facts about {} that will shock you",
                "5 things about {} that sound fake but are true",
                "The top 3 secrets of {} revealed",
                "4 {} facts that will blow your mind",
            ],
            'question': [
                "Ever wondered why {}?",
                "What if I told you that {}?",
                "Did you know that {} can actually...?",
                "Have you ever asked yourself why {}?",
            ],
            'challenge': [
                "Bet you didn't know this about {}",
                "Try to guess what happens when...",
                "Can you believe that {}?",
                "Most people get this wrong about {}",
            ]
        }
        
        self.retention_techniques = [
            "wait_for_it", "but_thats_not_all", "plot_twist", 
            "countdown", "cliffhanger", "shocking_reveal"
        ]
        
        self.seasonal_topics = self._get_seasonal_topics()
    
    def _get_seasonal_topics(self):
        """Get seasonal and holiday-themed topics."""
        return {
            'winter': ['snow', 'ice', 'winter animals', 'cold weather', 'holidays'],
            'spring': ['flowers', 'baby animals', 'growth', 'rain', 'renewal'],
            'summer': ['sun', 'heat', 'vacation', 'beach', 'summer activities'],
            'fall': ['leaves', 'harvest', 'migration', 'autumn colors', 'preparation'],
            'christmas': ['reindeer', 'snow', 'winter traditions', 'gift-giving animals'],
            'halloween': ['bats', 'spiders', 'nocturnal animals', 'scary facts'],
            'valentine': ['animal love', 'mating rituals', 'heart facts', 'romantic animals'],
            'new_year': ['time', 'calendars', 'resolutions', 'fresh starts'],
        }
    
    def get_current_season(self):
        """Determine current season for seasonal content."""
        month = datetime.now().month
        if month in [12, 1, 2]:
            return 'winter'
        elif month in [3, 4, 5]:
            return 'spring'
        elif month in [6, 7, 8]:
            return 'summer'
        else:
            return 'fall'
    
    def get_upcoming_holidays(self, days_ahead=30):
        """Get upcoming holidays for themed content."""
        today = datetime.now()
        upcoming = []
        
        # Major holidays to check
        holidays = {
            'Christmas': (12, 25),
            'Halloween': (10, 31),
            'Valentine\'s Day': (2, 14),
            'New Year': (1, 1),
            'Easter': None,  # Variable date
            'Thanksgiving': None,  # Variable date
        }
        
        for holiday, date in holidays.items():
            if date:
                month, day = date
                holiday_date = datetime(today.year, month, day)
                if holiday_date < today:
                    holiday_date = datetime(today.year + 1, month, day)
                
                days_until = (holiday_date - today).days
                if 0 <= days_until <= days_ahead:
                    upcoming.append((holiday, days_until))
        
        return upcoming
    
    def generate_viral_hook(self, topic, hook_type='random'):
        """Generate viral hooks for better engagement."""
        if hook_type == 'random':
            hook_type = random.choice(list(self.viral_hooks.keys()))
        
        hooks = self.viral_hooks.get(hook_type, self.viral_hooks['curiosity'])
        hook_template = random.choice(hooks)
        
        try:
            return hook_template.format(topic)
        except:
            return hook_template.replace('{}', topic)
    
    def enhance_script_with_hooks(self, original_script, topic):
        """Enhance script with viral hooks and retention techniques."""
        # Add viral hook at the beginning
        hook = self.generate_viral_hook(topic)
        
        # Add retention technique
        retention = random.choice(self.retention_techniques)
        
        enhanced_script = self._apply_retention_technique(original_script, retention)
        
        # Combine hook with enhanced script
        if not enhanced_script.startswith(hook):
            enhanced_script = f"{hook} {enhanced_script}"
        
        return enhanced_script
    
    def _apply_retention_technique(self, script, technique):
        """Apply specific retention techniques to the script."""
        if technique == "wait_for_it":
            return f"Wait for it... {script} Mind blown, right?"
        
        elif technique == "but_thats_not_all":
            parts = script.split('. ')
            if len(parts) > 1:
                return f"{parts[0]}. But that's not all! {'. '.join(parts[1:])}"
            return f"{script} But that's not all - there's more!"
        
        elif technique == "plot_twist":
            return f"{script} Plot twist: This actually helps them survive!"
        
        elif technique == "countdown":
            return f"Here's fact number 1: {script} Stay tuned for more shocking facts!"
        
        elif technique == "cliffhanger":
            return f"{script} But wait until you hear what happens next..."
        
        elif technique == "shocking_reveal":
            return f"Prepare to be shocked: {script} Incredible, isn't it?"
        
        return script
    
    def get_trending_format(self):
        """Get trending video formats."""
        formats = [
            {
                'name': 'mind_blown',
                'template': "🤯 {} facts that will blow your mind!",
                'style': 'shocking'
            },
            {
                'name': 'vs_battle',
                'template': "{} vs {} - You won't believe the winner!",
                'style': 'competitive'
            },
            {
                'name': 'secret_revealed',
                'template': "The secret about {} that scientists don't want you to know",
                'style': 'mysterious'
            },
            {
                'name': 'countdown',
                'template': "Top 3 {} facts in 60 seconds",
                'style': 'urgent'
            },
            {
                'name': 'myth_buster',
                'template': "Busting myths about {} - The truth will shock you!",
                'style': 'educational'
            }
        ]
        
        return random.choice(formats)
    
    def generate_seasonal_content(self, base_topic):
        """Generate seasonal variations of content."""
        season = self.get_current_season()
        upcoming_holidays = self.get_upcoming_holidays()
        
        seasonal_topics = self.seasonal_topics.get(season, [])
        
        # Check for upcoming holidays
        if upcoming_holidays:
            holiday, days_until = upcoming_holidays[0]
            if days_until <= 7:  # Within a week
                holiday_key = holiday.lower().replace(' ', '').replace("'", "")
                if holiday_key in self.seasonal_topics:
                    seasonal_topics.extend(self.seasonal_topics[holiday_key])
        
        if seasonal_topics:
            seasonal_angle = random.choice(seasonal_topics)
            return f"{base_topic} during {season}" if random.choice([True, False]) else f"{seasonal_angle} and {base_topic}"
        
        return base_topic
    
    def optimize_title_for_seo(self, title, topic):
        """Optimize title for YouTube SEO."""
        # High-performing keywords for YouTube Shorts
        power_words = [
            "Amazing", "Incredible", "Shocking", "Unbelievable", "Mind-blowing",
            "Secret", "Hidden", "Mysterious", "Fascinating", "Stunning"
        ]
        
        # Add power word if not present
        has_power_word = any(word.lower() in title.lower() for word in power_words)
        if not has_power_word:
            power_word = random.choice(power_words)
            title = f"{power_word} {title}"
        
        # Ensure optimal length (60-70 characters for best visibility)
        if len(title) > 70:
            title = title[:67] + "..."
        
        # Add trending hashtags
        hashtags = self._get_trending_hashtags(topic)
        
        return title, hashtags
    
    def _get_trending_hashtags(self, topic):
        """Get trending hashtags for the topic."""
        base_tags = ["shorts", "viral", "trending", "fyp", "facts"]
        
        topic_tags = {
            'animals': ["animals", "wildlife", "nature", "pets"],
            'space': ["space", "astronomy", "universe", "nasa"],
            'technology': ["tech", "ai", "future", "innovation"],
            'science': ["science", "research", "discovery", "experiment"],
            'history': ["history", "ancient", "historical", "past"],
            'cars': ["cars", "automotive", "vehicles", "racing"],
            'food': ["food", "cooking", "nutrition", "health"],
            'geography': ["geography", "earth", "travel", "world"]
        }
        
        specific_tags = topic_tags.get(topic.lower(), [topic.lower()])
        
        return base_tags + specific_tags
    
    def generate_engagement_cta(self):
        """Generate call-to-action for engagement."""
        ctas = [
            "Drop a 🤯 if this blew your mind!",
            "Comment your favorite fact below! 👇",
            "Share this with someone who loves facts! 📤",
            "Which fact surprised you most? Let me know! 💭",
            "Hit that like if you learned something new! 👍",
            "Subscribe for daily mind-blowing facts! 🔔",
            "Tag a friend who needs to see this! 👥",
            "What topic should I cover next? Comment below! 🤔"
        ]
        
        return random.choice(ctas)
    
    def create_series_concept(self, topic):
        """Create series concepts for consistent content."""
        series_formats = [
            f"Daily {topic.capitalize()} Facts",
            f"Mind-Blowing {topic.capitalize()} Series",
            f"Everything You Didn't Know About {topic.capitalize()}",
            f"The Ultimate {topic.capitalize()} Facts Collection",
            f"Shocking {topic.capitalize()} Secrets Revealed"
        ]
        
        return random.choice(series_formats)
    
    def get_optimal_posting_time(self):
        """Suggest optimal posting times based on audience engagement."""
        # Peak times for YouTube Shorts (general guidelines)
        peak_times = [
            {"time": "6:00-9:00 AM", "reason": "Morning commute"},
            {"time": "12:00-2:00 PM", "reason": "Lunch break"},
            {"time": "7:00-10:00 PM", "reason": "Evening entertainment"},
            {"time": "Weekend afternoons", "reason": "Leisure browsing"}
        ]
        
        return random.choice(peak_times)
