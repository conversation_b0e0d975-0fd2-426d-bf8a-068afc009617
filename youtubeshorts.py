import re
import random
import logging
import requests
import textwrap
import os
import json
import asyncio

import openai
from gtts import gTTS
import edge_tts
from moviepy.config import change_settings
from voice_config import get_voice_for_topic, get_voice_description, VOICE_SETTINGS
from video_effects import VideoEffects
from content_strategy import ContentStrategy
from thumbnail_generator import ThumbnailGenerator
from moviepy.editor import (
    ImageClip, AudioFileClip, TextClip, CompositeVideoClip,
    concatenate_videoclips, ColorClip
)
from moviepy.video.fx.fadein import fadein
from moviepy.video.fx.fadeout import fadeout
from moviepy.audio.AudioClip import CompositeAudioClip

# -- YOUTUBE API IMPORTS --
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials  # for reading/writing token info

# Configure ImageMagick Path
change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.1-Q16-HDRI\\magick.exe"})

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# ----------------------------
# 1) Fact History Utilities
# ----------------------------

def load_known_facts(file_path="facts_history.json"):
    """
    Load a list of known facts from a JSON file, or return an empty list
    if the file doesn't exist or is invalid.
    """
    if not os.path.exists(file_path):
        return []
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except:
        # If the file is empty/corrupt
        return []


def save_known_facts(facts, file_path="facts_history.json"):
    """Save the updated list of known facts to a JSON file."""
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(facts, f, indent=2)


# ----------------------------
# 2) Topic Count Utilities (If you still want to track "Part X")
# ----------------------------
# (Optional: if you still want Part X in your titles, keep these functions.)
def load_topic_counts(file_path="topic_counts.json"):
    """Load a dictionary of topic => count from a JSON file."""
    if not os.path.exists(file_path):
        return {}
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except:
        return {}


def save_topic_counts(counts, file_path="topic_counts.json"):
    """Save the dictionary of topic => count to a JSON file."""
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(counts, f, indent=2)


# ----------------------------
# 3) Existing Logic
# ----------------------------

def predefined_topics():
    return ["animals", "space", "cars", "technology", "history", "science", "food and drink", "geography"]


def fetch_trending_topic():
    try:
        topics = predefined_topics()
        return random.choice(topics)
    except Exception as e:
        logger.error(f"Error selecting predefined topics: {e}")
        return "technology"


def generate_enhanced_script(topic, content_strategy):
    """
    Create an enhanced script with viral hooks and engagement techniques.
    """
    try:
        # Generate base script first
        base_script = generate_base_script(topic)

        # Enhance with viral hooks and retention techniques
        enhanced_script = content_strategy.enhance_script_with_hooks(base_script, topic)

        # Add seasonal elements if relevant
        enhanced_script = content_strategy.generate_seasonal_content(enhanced_script)

        return enhanced_script

    except Exception as e:
        logger.error(f"Error generating enhanced script: {e}")
        return generate_base_script(topic)

def generate_base_script(topic):
    """
    Create a short fact about a subtopic (original function).
    """
    try:
        subtopics = {
            "animals": [
                "wildlife",
                "endangered species",
                "amazing animal abilities",
                "hybrid animals",
                "cats",
                "dogs",
                "animal intelligence",
                "animal communication",
                "marine life",
                "animal adaptations",
                "animal migration patterns",  # New
                "nocturnal animals",  # New
                "animal parenting behaviors"  # New
            ],
            "space": [
                "planets",
                "black holes",
                "astronomy discoveries",
                "space exploration",
                "stars and galaxies",
                "the Sun and solar flares",
                "asteroids and comets",
                "time and relativity",
                "the life cycle of stars",  # New
                "the search for habitable planets"  # New
            ],
            "cars": [
                "electric vehicles",
                "vintage cars",
                "future car technologies",
                "super cars",
                "Drones cars",
                "self-driving cars",
                "car safety innovations",
                "car racing",
                "hydrogen fuel cells",
                "the history of car manufacturing",  # New
                "the impact of cars on the environment"  # New
            ],
            "technology": [
                "AI innovations",
                "robotics",
                "breakthroughs in computing",
                "quantum computing",
                "augmented reality",
                "cybersecurity",
                "blockchain technology",
                "wearable technology",
                "the evolution of the internet",  # New
                "the impact of 5G on connectivity"  # New
            ],
            "history": [  # New topic
                "ancient civilizations",
                "famous historical figures",
                "pivotal moments in history",
                "the evolution of warfare",
                "the history of inventions"
            ],
            "science": [  # New topic
                "the periodic table and its secrets",
                "the science of climate change",
                "the human genome project",
                "the physics of everyday life",
                "the chemistry of food"
            ],
            "food and drink": [  # New topic
                "the history of chocolate",
                "the science of fermentation",
                "the cultural significance of spices",
                "the evolution of fast food",
                "natural healthy food",
                "the health benefits of superfoods"
            ],
            "geography": [  # New topic
                "the world’s most unique ecosystems",
                "the impact of natural disasters",
                "the history of map-making",
                "the science of volcanoes",
                "the cultural significance of rivers"
            ]
        }
        subtopic = random.choice(subtopics.get(topic, [topic]))
        prompt = (
            f"Write short fascinating fact about {subtopic}"
            f"Start with 'Did you know?' and keep it concise."
        )
        response = openai.ChatCompletion.create(
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=150
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating script with OpenAI: {e}")
        return f"Did you know? Here are some fascinating facts about {topic}."


def split_into_intro_and_two_parts(script):
    """
    Split the script into 3 parts (intro + part1 + part2).
    """
    import re
    sentences = re.split(r'([.?!])', script)

    cleaned_sentences = []
    for i in range(0, len(sentences), 2):
        if i + 1 < len(sentences):
            sentence = sentences[i].strip() + sentences[i + 1].strip()
            if sentence:
                cleaned_sentences.append(sentence)
        else:
            leftover = sentences[i].strip()
            if leftover:
                cleaned_sentences.append(leftover)

    if not cleaned_sentences:
        return script, "", ""

    intro = cleaned_sentences[0]
    rest = cleaned_sentences[1:]
    if not rest:
        return intro, "", ""

    half = len(rest) // 2
    part1 = " ".join(rest[:half]).strip()
    part2 = " ".join(rest[half:]).strip()

    return intro, part1, part2


def generate_3_images_single_request(full_prompt, api_key, model_id):
    valid_images = []
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "prompt": full_prompt,
            "modelId": model_id
        }
        response = requests.post("https://creator.aitubo.ai/api/job/create", json=payload, headers=headers)
        if response.status_code == 200:
            job_data = response.json()
            job_id = job_data['data']['id']
            status_url = f"https://creator.aitubo.ai/api/job/get?id={job_id}"

            while True:
                status_response = requests.get(status_url, headers=headers)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    if status_data['data']['status'] == 2:
                        images = status_data['data']['result']['data']['images']
                        domain = status_data['data']['result']['data']['domain']
                        for i in range(3):
                            if i < len(images):
                                img_rel_path = images[i]
                                output_file = f"image_{i}.jpg"
                                img_data = requests.get(domain + img_rel_path).content
                                with open(output_file, 'wb') as f:
                                    f.write(img_data)
                                valid_images.append(output_file)
                            else:
                                valid_images.append(None)
                        break
                    elif status_data['data']['status'] == 3:
                        logger.error("Image generation failed.")
                        break
                else:
                    logger.error(f"Aitubo status check error: {status_response.text}")
                    break
                logger.info("Waiting for image generation...")
        else:
            logger.error(f"Aitubo request error: {response.status_code}, {response.text}")
    except Exception as e:
        logger.error(f"Error generating images with Aitubo: {e}")

    while len(valid_images) < 3:
        valid_images.append(None)

    return valid_images


def get_random_voice():
    """
    Get a random high-quality voice from Edge-TTS.
    Returns a tuple of (voice_name, description)
    """
    voices = [
        ("en-US-AriaNeural", "Female, conversational and natural"),
        ("en-US-JennyNeural", "Female, friendly and warm"),
        ("en-US-GuyNeural", "Male, conversational and clear"),
        ("en-US-DavisNeural", "Male, expressive and engaging"),
        ("en-US-AmberNeural", "Female, warm and professional"),
        ("en-US-AnaNeural", "Female, young and energetic"),
        ("en-US-ChristopherNeural", "Male, mature and authoritative"),
        ("en-US-ElizabethNeural", "Female, mature and sophisticated"),
        ("en-US-EricNeural", "Male, young and enthusiastic"),
        ("en-US-MichelleNeural", "Female, professional and clear")
    ]
    return random.choice(voices)

async def generate_audio_with_edge_tts(text, output_file, voice="en-US-AriaNeural"):
    """
    Generate audio using Edge-TTS with natural-sounding voices.
    Available voices include many high-quality neural voices.
    """
    try:
        communicate = edge_tts.Communicate(text, voice)
        await communicate.save(output_file)
        return True
    except Exception as e:
        logger.error(f"Error generating audio with Edge-TTS: {e}")
        return False

def generate_audio_for_chunks(chunks, use_edge_tts=True, voice="en-US-AriaNeural"):
    """
    Generate audio for text chunks using either Edge-TTS (recommended) or gTTS as fallback.

    Args:
        chunks: List of text chunks
        use_edge_tts: Whether to use Edge-TTS (True) or gTTS (False)
        voice: Voice to use for Edge-TTS
    """
    audio_files = []

    for i, text in enumerate(chunks):
        text = text.strip()
        if not text:
            audio_files.append(None)
            continue

        output_file = f"narration_{i}.mp3"

        try:
            if use_edge_tts:
                # Use Edge-TTS for natural-sounding voice
                success = asyncio.run(generate_audio_with_edge_tts(text, output_file, voice))
                if success:
                    logger.info(f"Generated audio with Edge-TTS for chunk {i}: {text}")
                    audio_files.append(output_file)
                else:
                    # Fallback to gTTS if Edge-TTS fails
                    logger.warning(f"Edge-TTS failed for chunk {i}, falling back to gTTS")
                    tts = gTTS(text, lang='en', slow=False)
                    tts.save(output_file)
                    audio_files.append(output_file)
            else:
                # Use gTTS (original method)
                tts = gTTS(text, lang='en', slow=False)
                tts.save(output_file)
                audio_files.append(output_file)

        except Exception as e:
            logger.error(f"Error generating audio chunk {i}: {e}")
            audio_files.append(None)

    return audio_files


def pick_random_music(music_folder="music"):
    """
    Pick a random music file from the specified folder.
    """
    if not os.path.exists(music_folder):
        logger.error(f"Music folder '{music_folder}' does not exist.")
        return None

    music_files = [f for f in os.listdir(music_folder) if f.startswith("facts-") and f.endswith(".mp3")]
    if not music_files:
        logger.error(f"No music files found in '{music_folder}'.")
        return None

    selected_music = random.choice(music_files)
    return os.path.join(music_folder, selected_music)





def create_enhanced_video(chunks, image_files, audio_files, topic, output_file="output_video.mp4"):
    """Create enhanced video with professional effects and animations."""
    try:
        video_effects = VideoEffects()
        clips = []

        for idx, (chunk, img, audio) in enumerate(zip(chunks, image_files, audio_files)):
            if not chunk.strip() or not audio:
                continue

            # Load the audio clip and get its duration
            audio_clip = AudioFileClip(audio).volumex(2.5)  # Increase volume by 2.5x
            duration = audio_clip.duration

            if img:
                # Create image clip with SHORTS aspect ratio (9:16 vertical)
                phase_clip = ImageClip(img).set_duration(duration)

                # Resize to YouTube Shorts format (1080x1920 - 9:16 aspect ratio)
                phase_clip = phase_clip.resize(height=1920).resize(width=1080)

                # Add zoom/pan effects for visual interest (simplified)
                try:
                    effect_type = 'zoom_in' if idx == 0 else 'random'
                    phase_clip = video_effects.add_zoom_pan_effect(phase_clip, duration, effect_type)
                except Exception as e:
                    logger.warning(f"Zoom/pan effect failed: {e}")
                    # Keep original clip if effects fail
                    phase_clip = ImageClip(img).set_duration(duration).resize(height=1920).resize(width=1080)
            else:
                # Use YouTube Shorts dimensions (1080x1920)
                phase_clip = ColorClip(size=(1080, 1920), color=(0, 0, 0)).set_duration(duration)

            # Create text overlay for YouTube Shorts (vertical format)
            try:
                text_overlay = TextClip(
                    chunk,
                    fontsize=32,  # Larger font for mobile viewing
                    color='white',
                    method='caption',
                    size=(int(1080 * 0.9), None),  # Use 90% of width for Shorts
                    align='center'
                ).set_duration(duration)

                text_with_bg = text_overlay.on_color(
                    size=(text_overlay.w + 40, text_overlay.h + 40),
                    color=(0, 0, 0),
                    col_opacity=0.8,
                    pos=('center', 'center')
                )
                # Position text in lower third for Shorts
                text_with_bg = text_with_bg.set_position(("center", 1920 - text_overlay.h - 200))

                # Create composite with Shorts dimensions
                composite = CompositeVideoClip([phase_clip, text_with_bg], size=(1080, 1920)).set_duration(duration)

            except Exception as e:
                logger.warning(f"Text overlay failed: {e}")
                # Fallback to just the image/video without text
                composite = phase_clip.set_duration(duration)

            # Add simple fade effects
            try:
                if idx > 0:
                    composite = fadein(composite, 0.5)
                if idx < len(chunks) - 1:
                    composite = fadeout(composite, 0.5)
            except Exception as e:
                logger.warning(f"Fade effects failed: {e}")

            composite = composite.set_audio(audio_clip)
            clips.append(composite)

    except Exception as e:
        logger.error(f"Enhanced video creation failed: {e}")
        # Fallback to original video creation method
        return create_simple_video(chunks, image_files, audio_files, output_file)

    if not clips:
        raise RuntimeError("No valid clips were created.")

    # Concatenate all video clips (simplified to avoid transition issues)
    try:
        final_video = concatenate_videoclips(clips, method="compose")
    except Exception as e:
        logger.warning(f"Concatenation with compose failed: {e}")
        final_video = concatenate_videoclips(clips)

    # Add background music
    music_file = pick_random_music()
    if music_file:
        try:
            music_clip = AudioFileClip(music_file).set_duration(final_video.duration)
            # Lower the volume of the music (e.g., 20% of original volume)
            music_clip = music_clip.volumex(0.2)
            # Combine the original audio with the background music
            final_audio = CompositeAudioClip([final_video.audio, music_clip])
            final_video = final_video.set_audio(final_audio)
            logger.info(f"Added background music: {music_file}")
        except Exception as e:
            logger.warning(f"Background music failed: {e}")
    else:
        logger.warning("No background music was added.")

    # Write the final video to file with YouTube Shorts optimization
    final_video.write_videofile(
        output_file,
        fps=30,  # Higher FPS for Shorts
        codec="libx264",
        audio_codec="aac",
        bitrate="8000k",  # Higher bitrate for quality
        preset="medium"  # Good balance of quality and encoding speed
    )
    return output_file

def create_simple_video(chunks, image_files, audio_files, output_file="output_video.mp4"):
    """Simple video creation as fallback when enhanced version fails."""
    clips = []

    for idx, (chunk, img, audio) in enumerate(zip(chunks, image_files, audio_files)):
        if not chunk.strip() or not audio:
            continue

        # Load the audio clip and get its duration
        audio_clip = AudioFileClip(audio).volumex(2.5)
        duration = audio_clip.duration

        if img:
            # YouTube Shorts format (9:16 aspect ratio)
            phase_clip = ImageClip(img).set_duration(duration).resize(height=1920).resize(width=1080)
        else:
            phase_clip = ColorClip(size=(1080, 1920), color=(0, 0, 0)).set_duration(duration)

        # Simple text overlay for Shorts
        try:
            text_overlay = TextClip(
                chunk,
                fontsize=32,  # Larger for mobile
                color='white',
                method='caption',
                size=(int(1080 * 0.8), None),
                align='center'
            ).set_duration(duration)

            text_with_bg = text_overlay.on_color(
                size=(text_overlay.w + 40, text_overlay.h + 40),
                color=(0, 0, 0),
                col_opacity=0.8,
                pos=('center', 'center')
            )
            text_with_bg = text_with_bg.set_position(("center", 1920 - text_overlay.h - 200))
            composite = CompositeVideoClip([phase_clip, text_with_bg], size=(1080, 1920)).set_duration(duration)
        except Exception as e:
            logger.warning(f"Text overlay failed in simple mode: {e}")
            composite = phase_clip

        composite = composite.set_audio(audio_clip)
        clips.append(composite)

    if not clips:
        raise RuntimeError("No valid clips were created.")

    # Simple concatenation
    final_video = concatenate_videoclips(clips)

    # Add background music
    music_file = pick_random_music()
    if music_file:
        try:
            music_clip = AudioFileClip(music_file).set_duration(final_video.duration)
            music_clip = music_clip.volumex(0.2)
            final_audio = CompositeAudioClip([final_video.audio, music_clip])
            final_video = final_video.set_audio(final_audio)
            logger.info(f"Added background music: {music_file}")
        except Exception as e:
            logger.warning(f"Background music failed: {e}")

    # Write the final video to file with YouTube Shorts optimization
    final_video.write_videofile(
        output_file,
        fps=30,  # Higher FPS for Shorts
        codec="libx264",
        audio_codec="aac",
        bitrate="8000k",  # Higher bitrate for quality
        preset="medium"  # Good balance of quality and encoding speed
    )
    return output_file

def create_enhanced_thumbnail(image_files, title, topic):
    """Create an enhanced thumbnail using the thumbnail generator."""
    try:
        thumbnail_gen = ThumbnailGenerator()

        if image_files and image_files[0]:
            # Create main thumbnail
            thumbnail_path = thumbnail_gen.create_thumbnail(image_files[0], title, topic)

            if thumbnail_path:
                logger.info(f"Created enhanced thumbnail: {thumbnail_path}")
                return thumbnail_path
            else:
                logger.warning("Enhanced thumbnail creation failed, using original image")
                return image_files[0]

        return None

    except Exception as e:
        logger.warning(f"Thumbnail generation failed: {e}")
        # Return original image as fallback
        if image_files and image_files[0]:
            return image_files[0]
        return None


# ----------------------------
# 4) YouTube Upload Functions
# ----------------------------

SCOPES = ["https://www.googleapis.com/auth/youtube.upload"]


def get_authenticated_service():
    """
    Loads existing credentials from 'token.json' (if available).
    If they're missing or invalid, runs the OAuth flow and saves new creds.
    """
    creds = None
    token_path = "token.json"  # We'll save/load your YouTube token here
    client_secrets_file = "client_secret.json"

    # 1) Load existing credentials if present
    if os.path.exists(token_path):
        try:
            with open(token_path, 'r') as token_file:
                data = json.load(token_file)
                creds = Credentials.from_authorized_user_info(data, SCOPES)
        except:
            creds = None

    # 2) If no creds or creds invalid/expired, do the OAuth flow
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            logger.info("Refreshing token...")
            creds.refresh(Request())
        else:
            logger.info("No valid credentials found. Opening browser for sign-in...")
            flow = InstalledAppFlow.from_client_secrets_file(client_secrets_file, SCOPES)
            creds = flow.run_local_server(port=8080, prompt="consent", authorization_prompt_message="")

        # 3) Save the new credentials to token.json
        with open(token_path, 'w') as token_file:
            token_file.write(creds.to_json())
            logger.info(f"Saved YouTube credentials to {token_path}")

    # 4) Build the YouTube client
    youtube = build("youtube", "v3", credentials=creds)
    return youtube


def upload_video(file_path, title, description, thumbnail_path=None, privacy_status="public", tags=None):
    """
    Upload a video to YouTube with the given title, description, etc.
    and explicitly declare `selfDeclaredMadeForKids=False`.
    """
    youtube = get_authenticated_service()

    request_body = {
        "snippet": {
            "title": title,
            "description": description,
            "tags": tags if tags else []  # Add tags here
        },
        "status": {
            "privacyStatus": privacy_status,
            "selfDeclaredMadeForKids": False
        }
    }

    media = MediaFileUpload(file_path, chunksize=-1, resumable=True)

    request = youtube.videos().insert(
        part="snippet,status",
        body=request_body,
        media_body=media
    )

    response = None
    while response is None:
        status, response = request.next_chunk()
        if status:
            logger.info(f"Upload progress: {int(status.progress() * 100)}%")

    video_id = response.get("id")
    logger.info(f"Video uploaded to YouTube with videoId={video_id}")

    if thumbnail_path:
        set_thumbnail(youtube, video_id, thumbnail_path)
        logger.info(f"Thumbnail set from: {thumbnail_path}")

    return video_id

def create_playlist(youtube, title, description="", privacy_status="public"):
    """
    Create a new playlist on YouTube.
    """
    try:
        request_body = {
            "snippet": {
                "title": title,
                "description": description
            },
            "status": {
                "privacyStatus": privacy_status
            }
        }
        request = youtube.playlists().insert(
            part="snippet,status",
            body=request_body
        )
        response = request.execute()
        logger.info(f"Playlist created: {response['id']}")
        return response["id"]
    except Exception as e:
        logger.error(f"Error creating playlist: {e}")
        return None


def add_video_to_playlist(youtube, playlist_id, video_id):
    """
    Add a video to a playlist on YouTube.
    """
    try:
        request_body = {
            "snippet": {
                "playlistId": playlist_id,
                "resourceId": {
                    "kind": "youtube#video",
                    "videoId": video_id
                }
            }
        }
        request = youtube.playlistItems().insert(
            part="snippet",
            body=request_body
        )
        response = request.execute()
        logger.info(f"Video added to playlist: {playlist_id}")
        return response
    except Exception as e:
        logger.error(f"Error adding video to playlist: {e}")
        return None


def get_playlist_id(youtube, playlist_title):
    """
    Check if a playlist with the given title already exists.
    If it exists, return its ID. Otherwise, return None.
    """
    try:
        request = youtube.playlists().list(
            part="snippet",
            mine=True,
            maxResults=50
        )
        response = request.execute()
        logger.info(f"Fetched playlists: {response.get('items', [])}")
        for item in response.get("items", []):
            if item["snippet"]["title"].lower() == playlist_title.lower():
                logger.info(f"Playlist found: {item['id']}")
                return item["id"]
        logger.info(f"No playlist found with title: {playlist_title}")
        return None
    except Exception as e:
        logger.error(f"Error fetching playlists: {e}")
        return None

def set_thumbnail(youtube, video_id, thumbnail_path):
    if not thumbnail_path or not os.path.exists(thumbnail_path):
        return None
    request = youtube.thumbnails().set(
        videoId=video_id,
        media_body=MediaFileUpload(thumbnail_path)
    )
    response = request.execute()
    return response

# -------------------------------
# 5) Unique Fact Generation
# -------------------------------

def generate_unique_fact(topic, known_facts, max_tries=5):
    """
    Generate a fact that isn't in known_facts, trying up to max_tries times.
    If we fail to get a new fact after max_tries, return None.
    """
    for attempt in range(max_tries):
        new_fact = generate_base_script(topic)
        if new_fact not in known_facts:
            return new_fact
        logger.info(f"Received a repeated fact. Retrying... (Attempt {attempt + 1} of {max_tries})")
    return None


# -------------------------------
# 6) MAIN WORKFLOW with YouTube
# -------------------------------

if __name__ == "__main__":
    try:
        openai.api_key = "********************************************************************************************************************************************************************"
        api_key = "api-31c9b615df3111efb2c42ebca0c49faf"
        model_id = "66b1cd96bfac25af6f5e0ad9"

        # Initialize enhancement modules
        content_strategy = ContentStrategy()

        # 1) Load known facts so we don't repeat
        known_facts = load_known_facts("facts_history.json")

        # 2) Load topic counts to track part numbers
        topic_counts = load_topic_counts("topic_counts.json")

        topic = fetch_trending_topic()
        logger.info(f"Topic: {topic}")

        # 3) Generate enhanced script with viral hooks
        base_fact = generate_unique_fact(topic, known_facts, max_tries=5)
        if not base_fact:
            base_fact = f"Did you know? Here are some brand new facts about {topic}."
            logger.info("Couldn't get a unique fact after multiple tries, using fallback.")

        # Enhance script with content strategy
        enhanced_script = content_strategy.enhance_script_with_hooks(base_fact, topic)
        logger.info(f"Enhanced Script: {enhanced_script}")

        # 4) Add newly accepted fact to known_facts
        if base_fact not in known_facts:
            known_facts.append(base_fact)
            save_known_facts(known_facts, "facts_history.json")

        # 5) Generate optimized title
        current_count = topic_counts.get(topic, 0) + 1
        topic_counts[topic] = current_count
        save_topic_counts(topic_counts, "topic_counts.json")

        # Get trending format and optimize title
        trending_format = content_strategy.get_trending_format()
        base_title = f"Fascinating {topic.capitalize()} Fact (Part {current_count})"
        optimized_title, hashtags = content_strategy.optimize_title_for_seo(base_title, topic)

        logger.info(f"Optimized Title: {optimized_title}")
        logger.info(f"Hashtags: {hashtags}")

        # 6) Use enhanced script
        script = enhanced_script
        logger.info(f"Final Script: {script}")

        # 7) Split script into chunks
        chunk0, chunk1, chunk2 = split_into_intro_and_two_parts(script)
        chunks = [chunk0, chunk1, chunk2]
        logger.info(f"Chunks: {chunks}")

        # 8) Generate images (single request => 3 images)
        combined_prompt = " ".join(chunks).strip() or "random fallback prompt"
        image_files = generate_3_images_single_request(combined_prompt, api_key, model_id)
        logger.info(f"Image files: {image_files}")

        # 9) Generate audio for each chunk with Edge-TTS
        selected_voice = get_voice_for_topic(topic)
        voice_description = get_voice_description(selected_voice)
        logger.info(f"Selected voice for '{topic}': {selected_voice} ({voice_description})")

        use_edge_tts = VOICE_SETTINGS['primary_engine'] == 'edge-tts'
        audio_files = generate_audio_for_chunks(chunks, use_edge_tts=use_edge_tts, voice=selected_voice)
        logger.info(f"Audio files: {audio_files}")

        # 10) Create enhanced video with professional effects
        output_video = create_enhanced_video(chunks, image_files, audio_files, topic)
        logger.info(f"Created Enhanced Video: {output_video}")

        # 11) Create enhanced thumbnail
        enhanced_thumbnail = create_enhanced_thumbnail(image_files, optimized_title, topic)

        # 11) UPLOAD to YouTube
        description = (
            f"This short was automatically generated about {topic}.\n\n"
            f"Full fact:\n{script}\n\n"
            "⚠️ **Disclaimer for AI-Generated Content**: All facts, images, and content in this video are generated using artificial intelligence (AI). "
            "While we strive for accuracy, AI-generated content may occasionally contain errors or inconsistencies. "
            "We encourage you to conduct your own research and verify any facts or information presented in this video. "
            "Our goal is to inspire curiosity and learning, but it’s always a good idea to cross-check information from reliable sources. "
            "Thank you for watching, and remember: Stay curious, stay informed! 🧠\n\n"
            "🖼️ **AI-Generated Images**: The images in this video were created using AI and do not depict real people, events, or locations. "
            "Any resemblance to real individuals is purely coincidental and unintentional. These images are for entertainment and educational purposes only. "
            "Please note that the images may not always perfectly match the captions or text in the video.\n\n"
            "👉 Don’t forget to like, comment, and subscribe for more fascinating facts! 🔔"
        )
        # Define default tags
        default_tags = ["didyouknow", "didyou", "fact", "facts"]

        thumbnail_path = image_files[0] if image_files and image_files[0] else None

        # Generate engagement-focused description
        engagement_cta = content_strategy.generate_engagement_cta()
        series_concept = content_strategy.create_series_concept(topic)

        enhanced_description = (
            f"🎯 {trending_format['template'].format(topic.capitalize())}\n\n"
            f"📚 {script}\n\n"
            f"🔥 Part of our '{series_concept}' series!\n\n"
            f"💬 {engagement_cta}\n\n"
            f"🏷️ #Shorts #{' #'.join(hashtags)}\n\n"
            "⚠️ **AI-Generated Content**: Created using AI for educational entertainment. "
            "Please verify facts from reliable sources. Images are AI-generated.\n\n"
            "🎬 **Optimized for Mobile**: Vertical format with large text for the best mobile viewing experience!\n\n"
            "🔔 Subscribe for daily mind-blowing facts in Shorts format!"
        )

        # Add #Shorts to title to ensure YouTube recognizes it as a Short
        if "#Shorts" not in optimized_title and "#shorts" not in optimized_title.lower():
            shorts_title = f"{optimized_title} #Shorts"
        else:
            shorts_title = optimized_title

        # Use enhanced tags with YouTube Shorts specific tags
        enhanced_tags = ["Shorts", "YouTubeShorts", "Short"] + hashtags + ["viral", "trending", "fyp", "facts", "educational"]

        video_id = upload_video(
            file_path=output_video,
            title=shorts_title,
            description=enhanced_description,
            thumbnail_path=enhanced_thumbnail or (image_files[0] if image_files and image_files[0] else None),
            privacy_status="public",
            tags=enhanced_tags
        )
        logger.info(f"Video successfully uploaded. YouTube video ID: {video_id}")

        # 12) Add video to playlist
        youtube = get_authenticated_service()
        playlist_title = f"{topic.capitalize()} Facts"
        playlist_id = get_playlist_id(youtube, playlist_title)

        if not playlist_id:
            # Create a new playlist if it doesn't exist
            playlist_id = create_playlist(youtube, playlist_title, description=f"All videos about {topic}.")
            logger.info(f"Created new playlist: {playlist_title}")

        # Add the video to the playlist
        add_video_to_playlist(youtube, playlist_id, video_id)
        logger.info(f"Added video to playlist: {playlist_title}")

    except Exception as e:
        logger.error(f"An error occurred: {e}")

        