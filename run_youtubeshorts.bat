@echo off
REM Change to the project root
cd /d "C:\Users\<USER>\PycharmProjects\youtubeshorts"

REM Activate the virtual environment using the full path
call "C:\Users\<USER>\PycharmProjects\youtubeshorts\.venv\Scripts\activate.bat"

REM (Optional) Verify activation by echoing the VIRTUAL_ENV variable
echo Virtual environment is: %VIRTUAL_ENV%

REM Change to the directory where the Python script is located
cd /d "C:\Users\<USER>\PycharmProjects\youtubeshorts\youtubeshorts"

REM Run the Python script (now using the venv’s python)
python "youtubeshorts.py"
pause
