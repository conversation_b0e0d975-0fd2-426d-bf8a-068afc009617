#!C:\Users\<USER>\PycharmProjects\youtubeshorts\youtubeshorts\.venv\Scripts\python.exe

"""Shifts a subtitle by a fixed number of seconds."""

import datetime
import srt_tools.utils
import logging

log = logging.getLogger(__name__)


def parse_args():
    examples = {
        "Make all subtitles 5 seconds later": "srt fixed-timeshift --seconds 5",
        "Make all subtitles 5 seconds earlier": "srt fixed-timeshift --seconds -5",
    }

    parser = srt_tools.utils.basic_parser(description=__doc__, examples=examples)
    parser.add_argument(
        "--seconds", type=float, required=True, help="how many seconds to shift"
    )
    return parser.parse_args()


def scalar_correct_subs(subtitles, seconds_to_shift):
    td_to_shift = datetime.timedelta(seconds=seconds_to_shift)
    for subtitle in subtitles:
        subtitle.start += td_to_shift
        subtitle.end += td_to_shift
        yield subtitle


def main():
    args = parse_args()
    logging.basicConfig(level=args.log_level)
    srt_tools.utils.set_basic_args(args)
    corrected_subs = scalar_correct_subs(args.input, args.seconds)
    output = srt_tools.utils.compose_suggest_on_fail(corrected_subs, strict=args.strict)

    try:
        args.output.write(output)
    except (UnicodeEncodeError, TypeError):  # Python 2 fallback
        args.output.write(output.encode(args.encoding))


if __name__ == "__main__":  # pragma: no cover
    main()
