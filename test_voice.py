#!/usr/bin/env python3
"""
Test script to compare different TTS voices and demonstrate the enhanced voice quality.
"""

import asyncio
import os
import logging
from gtts import gTTS
import edge_tts

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_available_voices():
    """Get a list of high-quality Edge-TTS voices with descriptions."""
    return [
        ("en-US-AriaNeural", "Female, conversational and natural"),
        ("en-US-JennyNeural", "Female, friendly and warm"),
        ("en-US-GuyNeural", "Male, conversational and clear"),
        ("en-US-DavisNeural", "Male, expressive and engaging"),
        ("en-US-AmberNeural", "Female, warm and professional"),
        ("en-US-AnaNeural", "Female, young and energetic"),
        ("en-US-ChristopherNeural", "Male, mature and authoritative"),
        ("en-US-ElizabethNeural", "Female, mature and sophisticated"),
        ("en-US-EricNeural", "Male, young and enthusiastic"),
        ("en-US-MichelleNeural", "Female, professional and clear")
    ]

async def generate_edge_tts_sample(text, voice, output_file):
    """Generate a sample using Edge-TTS."""
    try:
        communicate = edge_tts.Communicate(text, voice)
        await communicate.save(output_file)
        logger.info(f"Generated Edge-TTS sample: {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error generating Edge-TTS sample: {e}")
        return False

def generate_gtts_sample(text, output_file):
    """Generate a sample using gTTS for comparison."""
    try:
        tts = gTTS(text, lang='en', slow=False)
        tts.save(output_file)
        logger.info(f"Generated gTTS sample: {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error generating gTTS sample: {e}")
        return False

async def main():
    """Main function to test different voices."""
    test_text = "Did you know? Octopuses have three hearts and blue blood! Two hearts pump blood to the gills, while the third pumps blood to the rest of the body. Their blue blood contains copper-based hemocyanin instead of iron-based hemoglobin, making it more efficient in cold, low-oxygen environments."
    
    print("🎙️  Voice Quality Test - Comparing TTS Options")
    print("=" * 60)
    
    # Create samples directory
    os.makedirs("voice_samples", exist_ok=True)
    
    # Generate gTTS sample for comparison
    print("\n1. Generating gTTS sample (original robotic voice)...")
    gtts_file = "voice_samples/gtts_sample.mp3"
    generate_gtts_sample(test_text, gtts_file)
    
    # Generate Edge-TTS samples
    print("\n2. Generating Edge-TTS samples (natural voices)...")
    voices = get_available_voices()
    
    for i, (voice_name, description) in enumerate(voices[:3], 1):  # Test first 3 voices
        print(f"   {i}. {voice_name} - {description}")
        output_file = f"voice_samples/edge_tts_{voice_name.replace('-', '_').lower()}.mp3"
        await generate_edge_tts_sample(test_text, voice_name, output_file)
    
    print("\n✅ Voice samples generated successfully!")
    print("\n📁 Check the 'voice_samples' folder to hear the difference:")
    print("   - gtts_sample.mp3 (original robotic voice)")
    print("   - edge_tts_*.mp3 (natural AI voices)")
    
    print("\n🎯 Recommendation: Edge-TTS voices sound much more natural and engaging!")
    print("   Your YouTube videos will have significantly better voice quality.")

if __name__ == "__main__":
    asyncio.run(main())
