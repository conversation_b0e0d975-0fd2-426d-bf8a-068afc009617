#!C:\Users\<USER>\PycharmProjects\youtubeshorts\youtubeshorts\.venv\Scripts\python.exe

"""Filter subtitles that match or don't match a particular pattern."""

import importlib
import srt_tools.utils
import logging

log = logging.getLogger(__name__)


def strip_to_matching_lines_only(subtitles, imports, func_str, invert, per_sub):
    for import_name in imports:
        real_import = importlib.import_module(import_name)
        globals()[import_name] = real_import

    raw_func = eval(func_str)  # pylint: disable-msg=eval-used

    if invert:
        func = lambda line: not raw_func(line)
    else:
        func = raw_func

    for subtitle in subtitles:
        if per_sub:
            if not func(subtitle.content):
                subtitle.content = ""
        else:
            subtitle.content = "\n".join(
                line for line in subtitle.content.splitlines() if func(line)
            )

        yield subtitle


def parse_args():
    examples = {
        "Only include Chinese lines": "srt lines-matching -m hanzidentifier -f hanzidentifier.has_chinese",
        "Exclude all lines which only contain numbers": "srt lines-matching -v -f 'lambda x: x.isdigit()'",
    }
    parser = srt_tools.utils.basic_parser(description=__doc__, examples=examples)
    parser.add_argument(
        "-f", "--func", help="a function to use to match lines", required=True
    )
    parser.add_argument(
        "-m",
        "--module",
        help="modules to import in the function context",
        action="append",
        default=[],
    )
    parser.add_argument(
        "-s",
        "--per-subtitle",
        help="match the content of each subtitle, not each line",
        action="store_true",
    )
    parser.add_argument(
        "-v",
        "--invert",
        help="invert matching -- only match lines returning False",
        action="store_true",
    )
    return parser.parse_args()


def main():
    args = parse_args()
    logging.basicConfig(level=args.log_level)
    srt_tools.utils.set_basic_args(args)
    matching_subtitles_only = strip_to_matching_lines_only(
        args.input, args.module, args.func, args.invert, args.per_subtitle
    )
    output = srt_tools.utils.compose_suggest_on_fail(
        matching_subtitles_only, strict=args.strict
    )

    try:
        args.output.write(output)
    except (UnicodeEncodeError, TypeError):  # Python 2 fallback
        args.output.write(output.encode(args.encoding))


if __name__ == "__main__":  # pragma: no cover
    main()
