"""
Smart thumbnail generator for YouTube Shorts.
Creates eye-catching thumbnails with text overlays, emotion-based designs, and A/B testing variants.
"""

import os
import random
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import requests
import logging

logger = logging.getLogger(__name__)

class ThumbnailGenerator:
    """Generate engaging thumbnails for YouTube Shorts."""
    
    def __init__(self):
        self.thumbnail_size = (1280, 720)  # YouTube thumbnail size
        self.colors = {
            'shocking': [(255, 0, 0), (255, 165, 0), (255, 255, 0)],  # Red, Orange, Yellow
            'educational': [(0, 100, 200), (0, 150, 255), (100, 200, 255)],  # Blues
            'nature': [(34, 139, 34), (0, 128, 0), (50, 205, 50)],  # Greens
            'space': [(25, 25, 112), (72, 61, 139), (123, 104, 238)],  # Purples
            'technology': [(105, 105, 105), (169, 169, 169), (192, 192, 192)],  # Grays
            'exciting': [(255, 20, 147), (255, 69, 0), (255, 215, 0)]  # Pink, Red-<PERSON>, <PERSON>
        }
        
        self.emotion_styles = {
            'shocking': {'font_size': 60, 'stroke_width': 4, 'effects': ['glow', 'shadow']},
            'curious': {'font_size': 50, 'stroke_width': 3, 'effects': ['outline']},
            'educational': {'font_size': 45, 'stroke_width': 2, 'effects': ['clean']},
            'exciting': {'font_size': 55, 'stroke_width': 4, 'effects': ['vibrant', 'shadow']}
        }
        
        self.power_words = [
            "SHOCKING", "AMAZING", "INCREDIBLE", "MIND-BLOWING", "SECRET",
            "HIDDEN", "REVEALED", "UNBELIEVABLE", "STUNNING", "MYSTERIOUS"
        ]
    
    def create_thumbnail(self, base_image_path, title, topic, style='auto'):
        """
        Create an engaging thumbnail from base image.
        
        Args:
            base_image_path: Path to the base image
            title: Video title
            topic: Content topic for style selection
            style: Thumbnail style ('shocking', 'educational', 'exciting', 'auto')
        """
        try:
            if not os.path.exists(base_image_path):
                logger.error(f"Base image not found: {base_image_path}")
                return None
            
            # Load and prepare base image
            base_image = Image.open(base_image_path)
            thumbnail = self._prepare_base_image(base_image)
            
            # Auto-select style based on topic
            if style == 'auto':
                style = self._select_style_for_topic(topic)
            
            # Add visual enhancements
            thumbnail = self._add_visual_effects(thumbnail, style)
            
            # Add text overlay
            thumbnail = self._add_text_overlay(thumbnail, title, style)
            
            # Add engagement elements
            thumbnail = self._add_engagement_elements(thumbnail, topic)
            
            # Save thumbnail
            output_path = f"thumbnail_{random.randint(1000, 9999)}.jpg"
            thumbnail.save(output_path, "JPEG", quality=95)
            
            logger.info(f"Thumbnail created: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error creating thumbnail: {e}")
            return None
    
    def _prepare_base_image(self, image):
        """Prepare base image for thumbnail creation."""
        # Resize to thumbnail dimensions
        image = image.resize(self.thumbnail_size, Image.Resampling.LANCZOS)
        
        # Enhance contrast and saturation for better visibility
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.2)
        
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.1)
        
        return image
    
    def _select_style_for_topic(self, topic):
        """Auto-select thumbnail style based on topic."""
        topic_styles = {
            'animals': 'exciting',
            'space': 'shocking',
            'technology': 'curious',
            'science': 'educational',
            'history': 'curious',
            'cars': 'exciting',
            'food': 'exciting',
            'geography': 'educational'
        }
        
        return topic_styles.get(topic.lower(), 'shocking')
    
    def _add_visual_effects(self, image, style):
        """Add visual effects based on style."""
        if style == 'shocking':
            # Add slight blur and vignette for dramatic effect
            image = self._add_vignette(image)
            
        elif style == 'exciting':
            # Increase saturation
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.3)
            
        elif style == 'educational':
            # Clean, professional look
            pass  # Keep original image clean
            
        return image
    
    def _add_vignette(self, image):
        """Add vignette effect to image."""
        # Create vignette mask
        mask = Image.new('L', image.size, 0)
        draw = ImageDraw.Draw(mask)
        
        # Draw gradient circle
        center_x, center_y = image.size[0] // 2, image.size[1] // 2
        max_radius = min(center_x, center_y)
        
        for i in range(max_radius):
            alpha = int(255 * (i / max_radius))
            draw.ellipse([
                center_x - i, center_y - i,
                center_x + i, center_y + i
            ], fill=alpha)
        
        # Apply vignette
        vignette = Image.new('RGB', image.size, (0, 0, 0))
        return Image.composite(image, vignette, mask)
    
    def _add_text_overlay(self, image, title, style):
        """Add text overlay to thumbnail."""
        draw = ImageDraw.Draw(image)
        
        # Get style settings
        style_config = self.emotion_styles.get(style, self.emotion_styles['shocking'])
        font_size = style_config['font_size']
        stroke_width = style_config['stroke_width']
        
        # Try to load custom font, fallback to default
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("Arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # Process title for better visibility
        processed_title = self._process_title_for_thumbnail(title)
        
        # Calculate text position
        text_bbox = draw.textbbox((0, 0), processed_title, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        x = (image.width - text_width) // 2
        y = image.height - text_height - 50  # Position near bottom
        
        # Select colors based on style
        colors = self.colors.get(style, self.colors['shocking'])
        text_color = colors[0]
        stroke_color = (255, 255, 255) if sum(text_color) < 400 else (0, 0, 0)
        
        # Draw text with stroke
        draw.text((x, y), processed_title, font=font, fill=text_color, 
                 stroke_width=stroke_width, stroke_fill=stroke_color)
        
        return image
    
    def _process_title_for_thumbnail(self, title):
        """Process title for better thumbnail visibility."""
        # Remove common prefixes
        title = title.replace("Did you know?", "").strip()
        
        # Add power word if not present
        has_power_word = any(word in title.upper() for word in self.power_words)
        if not has_power_word and len(title) < 40:
            power_word = random.choice(self.power_words)
            title = f"{power_word}: {title}"
        
        # Limit length for visibility
        if len(title) > 50:
            title = title[:47] + "..."
        
        return title.upper()
    
    def _add_engagement_elements(self, image, topic):
        """Add engagement elements like emojis and arrows."""
        draw = ImageDraw.Draw(image)
        
        # Topic-specific emojis
        topic_emojis = {
            'animals': '🐾',
            'space': '🚀',
            'technology': '🤖',
            'science': '🔬',
            'history': '🏛️',
            'cars': '🚗',
            'food': '🍕',
            'geography': '🌍'
        }
        
        emoji = topic_emojis.get(topic.lower(), '🤯')
        
        # Add emoji in corner (simplified - would need emoji font for actual implementation)
        # For now, add colored circle as placeholder
        circle_color = random.choice(self.colors['exciting'])
        draw.ellipse([20, 20, 80, 80], fill=circle_color)
        
        # Add arrow pointing to important area (simplified)
        arrow_points = [(image.width - 100, 100), (image.width - 50, 120), (image.width - 100, 140)]
        draw.polygon(arrow_points, fill=(255, 255, 0))
        
        return image
    
    def create_ab_test_variants(self, base_image_path, title, topic, num_variants=3):
        """Create multiple thumbnail variants for A/B testing."""
        variants = []
        styles = ['shocking', 'exciting', 'educational', 'curious']
        
        for i in range(num_variants):
            style = styles[i % len(styles)]
            variant_path = self.create_thumbnail(base_image_path, title, topic, style)
            if variant_path:
                variants.append({
                    'path': variant_path,
                    'style': style,
                    'variant': f'V{i+1}'
                })
        
        return variants
    
    def analyze_thumbnail_performance(self, thumbnail_data):
        """Analyze which thumbnail styles perform best (placeholder for future analytics)."""
        # This would integrate with YouTube Analytics API in a full implementation
        performance_tips = [
            "Bright colors tend to perform better",
            "Text should be large and readable on mobile",
            "Faces and emotions increase click-through rates",
            "High contrast between text and background is crucial",
            "Thumbnails with arrows and circles draw attention"
        ]
        
        return {
            'tips': performance_tips,
            'recommended_style': 'shocking',  # Based on general YouTube trends
            'color_scheme': 'high_contrast'
        }
    
    def create_series_thumbnail_template(self, series_name, episode_number):
        """Create consistent thumbnail template for series."""
        # Create base template
        template = Image.new('RGB', self.thumbnail_size, (50, 50, 50))
        draw = ImageDraw.Draw(template)
        
        # Add series branding
        try:
            font_large = ImageFont.truetype("arial.ttf", 40)
            font_small = ImageFont.truetype("arial.ttf", 30)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Series title
        draw.text((50, 50), series_name, font=font_large, fill=(255, 255, 255))
        
        # Episode number
        draw.text((50, 100), f"Episode {episode_number}", font=font_small, fill=(200, 200, 200))
        
        return template
