#!/usr/bin/env python3
"""
Quick test script to verify all enhancements are working properly.
"""

import logging
from video_effects import VideoEffects
from content_strategy import ContentStrategy
from thumbnail_generator import ThumbnailGenerator
from voice_config import get_voice_for_topic, get_voice_description

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_enhancements():
    """Test all enhancement modules."""
    print("🧪 Testing YouTube Channel Enhancements...")
    print("=" * 50)
    
    # Test Content Strategy
    print("\n1. Testing Content Strategy...")
    content_strategy = ContentStrategy()
    
    topic = "animals"
    viral_hook = content_strategy.generate_viral_hook(topic)
    trending_format = content_strategy.get_trending_format()
    engagement_cta = content_strategy.generate_engagement_cta()
    
    print(f"   ✅ Viral Hook: {viral_hook}")
    print(f"   ✅ Trending Format: {trending_format['name']}")
    print(f"   ✅ Engagement CTA: {engagement_cta}")
    
    # Test Voice Selection
    print("\n2. Testing Voice Selection...")
    voice = get_voice_for_topic(topic)
    description = get_voice_description(voice)
    
    print(f"   ✅ Selected Voice: {voice}")
    print(f"   ✅ Description: {description}")
    
    # Test Video Effects
    print("\n3. Testing Video Effects...")
    video_effects = VideoEffects()
    
    print(f"   ✅ Available Transitions: {video_effects.transition_types}")
    print(f"   ✅ Video Effects Module: Loaded successfully")
    
    # Test Thumbnail Generator
    print("\n4. Testing Thumbnail Generator...")
    thumbnail_gen = ThumbnailGenerator()
    
    print(f"   ✅ Thumbnail Size: {thumbnail_gen.thumbnail_size}")
    print(f"   ✅ Available Colors: {list(thumbnail_gen.colors.keys())}")
    
    # Test SEO Optimization
    print("\n5. Testing SEO Optimization...")
    title = "Amazing Animal Facts"
    optimized_title, hashtags = content_strategy.optimize_title_for_seo(title, topic)
    
    print(f"   ✅ Original Title: {title}")
    print(f"   ✅ Optimized Title: {optimized_title}")
    print(f"   ✅ Hashtags: {hashtags}")
    
    print("\n" + "=" * 50)
    print("🎉 All Enhancement Modules Working Perfectly!")
    print("🚀 Your YouTube channel is ready for professional content creation!")
    
    return True

if __name__ == "__main__":
    try:
        test_enhancements()
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        print("Please check the installation and try again.")
