"""
Enhanced video production system with dynamic visual effects, transitions, and animations.
This module adds professional-quality visual enhancements to make videos more engaging.
"""

import random
import numpy as np
from moviepy.editor import (
    VideoClip, ImageClip, TextClip, CompositeVideoClip,
    concatenate_videoclips, ColorClip, AudioFileClip
)
from moviepy.video.fx.fadein import fadein
from moviepy.video.fx.fadeout import fadeout
from moviepy.video.fx.resize import resize
import logging

logger = logging.getLogger(__name__)

class VideoEffects:
    """Professional video effects and transitions for YouTube Shorts."""
    
    def __init__(self):
        self.transition_types = [
            'fade', 'slide_left', 'slide_right', 'zoom_in', 'zoom_out', 'crossfade'
        ]
        
    def add_zoom_pan_effect(self, image_clip, duration, effect_type='random'):
        """
        Add cinematic zoom and pan effects to static images.
        
        Args:
            image_clip: MoviePy ImageClip
            duration: Duration of the effect
            effect_type: 'zoom_in', 'zoom_out', 'pan_left', 'pan_right', 'random'
        """
        if effect_type == 'random':
            effect_type = random.choice(['zoom_in', 'zoom_out', 'pan_left', 'pan_right'])
        
        try:
            if effect_type == 'zoom_in':
                return self._zoom_in_effect(image_clip, duration)
            elif effect_type == 'zoom_out':
                return self._zoom_out_effect(image_clip, duration)
            elif effect_type == 'pan_left':
                return self._pan_effect(image_clip, duration, direction='left')
            elif effect_type == 'pan_right':
                return self._pan_effect(image_clip, duration, direction='right')
            else:
                return image_clip.set_duration(duration)
        except Exception as e:
            logger.warning(f"Failed to apply {effect_type} effect: {e}")
            return image_clip.set_duration(duration)
    
    def _zoom_in_effect(self, clip, duration):
        """Smooth zoom-in effect."""
        def zoom_function(t):
            zoom_factor = 1 + (t / duration) * 0.3  # Zoom from 1x to 1.3x
            return zoom_factor
        
        return clip.resize(zoom_function).set_duration(duration)
    
    def _zoom_out_effect(self, clip, duration):
        """Smooth zoom-out effect."""
        def zoom_function(t):
            zoom_factor = 1.3 - (t / duration) * 0.3  # Zoom from 1.3x to 1x
            return zoom_factor
        
        return clip.resize(zoom_function).set_duration(duration)
    
    def _pan_effect(self, clip, duration, direction='left'):
        """Smooth panning effect."""
        def pan_function(t):
            if direction == 'left':
                x_offset = -(t / duration) * 100  # Pan left
            else:
                x_offset = (t / duration) * 100   # Pan right
            return ('center', 'center')
        
        # Resize clip to be larger than frame for panning
        resized_clip = clip.resize(1.2)
        return resized_clip.set_position(pan_function).set_duration(duration)
    
    def create_animated_text(self, text, duration, style='fade_in'):
        """
        Create animated text overlays with various effects.
        Simplified version to avoid ImageMagick issues.

        Args:
            text: Text to animate
            duration: Duration of animation
            style: 'fade_in', 'simple' (typewriter disabled due to ImageMagick issues)
        """
        try:
            # Use simple fade_in as default to avoid ImageMagick issues
            if style == 'fade_in' or style == 'typewriter':
                return self._fade_in_text(text, duration)
            elif style == 'simple':
                return self._simple_text(text, duration)
            else:
                return self._simple_text(text, duration)
        except Exception as e:
            logger.warning(f"Failed to create animated text: {e}")
            return self._simple_text(text, duration)
    
    def _typewriter_effect(self, text, duration):
        """Typewriter effect for text."""
        def make_frame(t):
            chars_to_show = int((t / duration) * len(text))
            partial_text = text[:chars_to_show]
            
            txt_clip = TextClip(
                partial_text,
                fontsize=24,
                color='white',
                font='Arial-Bold',
                stroke_color='black',
                stroke_width=2
            )
            return txt_clip.get_frame(0) if txt_clip.duration > 0 else None
        
        return VideoClip(make_frame, duration=duration)
    
    def _fade_in_text(self, text, duration):
        """Fade-in effect for text - simplified to avoid ImageMagick issues."""
        try:
            txt_clip = TextClip(
                text,
                fontsize=24,
                color='white',
                method='caption',
                size=(800, None),
                align='center'
            ).set_duration(duration)

            return fadein(txt_clip, min(duration * 0.3, 1.0))
        except Exception as e:
            logger.warning(f"TextClip creation failed: {e}")
            return self._simple_text(text, duration)
    
    def _slide_up_text(self, text, duration):
        """Slide-up effect for text."""
        txt_clip = TextClip(
            text,
            fontsize=24,
            color='white',
            font='Arial-Bold',
            stroke_color='black',
            stroke_width=2
        ).set_duration(duration)
        
        def position_function(t):
            y_pos = 720 - (t / duration) * 200  # Slide from bottom
            return ('center', y_pos)
        
        return txt_clip.set_position(position_function)
    
    def _bounce_text(self, text, duration):
        """Bounce effect for text."""
        txt_clip = TextClip(
            text,
            fontsize=28,
            color='yellow',
            font='Arial-Bold',
            stroke_color='black',
            stroke_width=3
        ).set_duration(duration)
        
        def size_function(t):
            bounce = 1 + 0.1 * np.sin(t * 10)  # Bounce effect
            return bounce
        
        return txt_clip.resize(size_function)
    
    def _simple_text(self, text, duration):
        """Simple text overlay without complex formatting."""
        try:
            return TextClip(
                text,
                fontsize=22,
                color='white',
                method='caption',
                size=(800, None),
                align='center'
            ).set_duration(duration)
        except Exception as e:
            logger.error(f"Even simple text creation failed: {e}")
            # Return a colored rectangle as absolute fallback
            return ColorClip(size=(800, 100), color=(0, 0, 0)).set_duration(duration)

    def _default_text(self, text, duration):
        """Default text overlay - alias for simple text."""
        return self._simple_text(text, duration)
    
    def add_transition(self, clip1, clip2, transition_type='fade', duration=0.5):
        """
        Add smooth transitions between clips.
        
        Args:
            clip1: First clip
            clip2: Second clip
            transition_type: Type of transition
            duration: Transition duration
        """
        try:
            if transition_type == 'fade':
                return self._fade_transition(clip1, clip2, duration)
            elif transition_type == 'slide_left':
                return self._slide_transition(clip1, clip2, duration, 'left')
            elif transition_type == 'slide_right':
                return self._slide_transition(clip1, clip2, duration, 'right')
            elif transition_type == 'zoom_in':
                return self._zoom_transition(clip1, clip2, duration, 'in')
            elif transition_type == 'zoom_out':
                return self._zoom_transition(clip1, clip2, duration, 'out')
            else:
                return concatenate_videoclips([clip1, clip2])
        except Exception as e:
            logger.warning(f"Failed to apply {transition_type} transition: {e}")
            return concatenate_videoclips([clip1, clip2])
    
    def _fade_transition(self, clip1, clip2, duration):
        """Crossfade transition between clips."""
        try:
            clip1_fade = fadeout(clip1, duration)
            clip2_fade = fadein(clip2, duration)

            # Overlap the clips
            clip2_fade = clip2_fade.set_start(clip1.duration - duration)
            return CompositeVideoClip([clip1_fade, clip2_fade])
        except Exception as e:
            logger.warning(f"Fade transition failed: {e}")
            # Return simple concatenation as fallback
            return concatenate_videoclips([clip1, clip2])
    
    def _slide_transition(self, clip1, clip2, duration, direction):
        """Slide transition between clips."""
        w, h = clip1.size
        
        if direction == 'left':
            clip2_pos = lambda t: (-w + (t/duration) * w, 0)
        else:
            clip2_pos = lambda t: (w - (t/duration) * w, 0)
        
        clip2_slide = clip2.set_position(clip2_pos).set_duration(duration)
        clip2_slide = clip2_slide.set_start(clip1.duration - duration)
        
        return CompositeVideoClip([clip1, clip2_slide])
    
    def _zoom_transition(self, clip1, clip2, duration, zoom_type):
        """Zoom transition between clips."""
        if zoom_type == 'in':
            clip1_zoom = clip1.resize(lambda t: 1 + (t/clip1.duration) * 0.2)
        else:
            clip2_zoom = clip2.resize(lambda t: 1.2 - (t/duration) * 0.2)
            clip2_zoom = clip2_zoom.set_start(clip1.duration - duration)
            return CompositeVideoClip([clip1, clip2_zoom])
        
        return concatenate_videoclips([clip1_zoom, clip2])
    
    def add_progress_bar(self, video_clip, position='bottom'):
        """Add a progress bar to show video progress."""
        duration = video_clip.duration
        w, h = video_clip.size
        
        def make_progress_bar(t):
            progress = t / duration
            bar_width = int(w * 0.8 * progress)
            
            # Create progress bar
            bar = ColorClip(size=(bar_width, 4), color=(255, 255, 255))
            bg = ColorClip(size=(int(w * 0.8), 4), color=(100, 100, 100))
            
            return CompositeVideoClip([bg, bar])
        
        progress_clip = VideoClip(make_progress_bar, duration=duration)
        
        if position == 'bottom':
            progress_clip = progress_clip.set_position(('center', h - 20))
        else:
            progress_clip = progress_clip.set_position(('center', 20))
        
        return CompositeVideoClip([video_clip, progress_clip])
    
    def add_subscribe_reminder(self, video_clip, start_time=None):
        """Add animated subscribe reminder."""
        if start_time is None:
            start_time = video_clip.duration * 0.7  # Show at 70% of video
        
        subscribe_text = TextClip(
            "👆 Subscribe for more facts! 👆",
            fontsize=20,
            color='yellow',
            font='Arial-Bold',
            stroke_color='red',
            stroke_width=2
        ).set_duration(3).set_start(start_time)
        
        # Add bounce animation
        def bounce_function(t):
            return 1 + 0.1 * np.sin(t * 8)
        
        subscribe_animated = subscribe_text.resize(bounce_function)
        subscribe_animated = subscribe_animated.set_position(('center', 50))
        
        return CompositeVideoClip([video_clip, subscribe_animated])
